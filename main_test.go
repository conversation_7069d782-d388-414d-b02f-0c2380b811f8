package main

import (
	"context"
	"crypto/des"
	"encoding/base64"
	"fmt"
	"log"
	"testing"

	"ccb/internal/data/rpcimpl"
	"ccb/internal/initialize"

	"github.com/sleepinggodoflove/lansexiongdi-marketing-sdk/api/v1/key"
	"github.com/sleepinggodoflove/lansexiongdi-marketing-sdk/core"
)

var (
	appId      = "JLYPGHBSDKok"
	privateKey = "MIIEowIBAAKCAQEA4YYRanaqAryAxemV+A9UfbKglFapYPsH9MSR83hwrE1we4hG47sN2f+agT6DF1vnb7MocChQzs0puJzJLHY53FK00fOgSmQ7IoE2cB/ywabNm4ynVMuJD0QCumb9m6wd9vT47R6eKL5ZQ5lP0pzPjw632rzQfOwTGAQeq6Sg9R73NQbL3r6Kk0KorTgF6F+in0PwAiaSoKSKMPcF2sNdZONtPcx2XAEt/5DHz4XkgCEVtxZCw1/lJegjlP8j/8Uy4BjOSxAWMrIu3C9kJxP2Dftc8bBFs8YVEA019Q2IP22Ws35QgnQ9+YyesPLLnJgjLkifsNgSj457+LlWR/rtOQIDAQABAoIBAQCXM47LpUF40i84AdLlXEnWeIOG0M+A0O1VotEDvnU0YDsQ5543orq3tNI+4s17RMfXMPPktnLvI8hcGOob4CwM1nqa5M9L8QVgreb0p8bXJOFm75dkl42N6Zg2p0RTMcNpRnb6a3O4d15bnxdQaogSLtj/U2YoyxJGIMT7aUXs8JK0AaZNTdjqeoJIxuRSYQ+Viu3nBuCqhMKeh579kFs4/8DD/+fADSa39w1skW0U064Ou0jKjqK8+rNTdx1ANyoxe2/9AJjLggIosswJStfI9BmPgd9MtATmi/DN7ceXNyfDqtyLgWmJD+Iad7XQFXrDSxUPbV7xVBK0N9Zz9+LxAoGBAO6zJZMIAGPwROZKNj7WmhjCNEdUBrV2zZHNo0ABwu+eeyrlX9dyf7Kg2O8j+wWs4lcQoDMjopaSoDCqlYBGAOEuTATbinhzURyT1+mMDRd3tHvaGB0xVSDCg9MKDOnXTSAI049Rw4qy4gxLw0aDOYVyQVeaRKOeICyQ2S/UZ4ktAoGBAPHec36J5L+k3WVVejMyh8xBjKucbPJ2QSQxprHovINq4fiZGoBHJWA8hnF4xffb8HO+1hSfs7iUnNpqUF9qqsq5uFEEFRJQHEVkabL92MKQW6oTvB/3AtuYo1ieL4Ri/s+p72GD2sEF85vfXoNMy9kGGTxuxXSaJcphANZh36O9AoGAHq2pq58ApdeQk2SAWk0smvgCzhQPlK0DOLUMQUK4siFVRSsbyU3u+Z9QolOmhJE2uefqHwh6cg3ARuD1u/nDTS2tGYa54kc36otZ2atlV1nMOYrLRJ09iqtVU/9br4Y7ZF2eCzKABkh9YwUtwNdGwk7v9PlfzwOYsQGg2FPdRrUCgYB7kkZ5kOPw0b9XRWuQerGMtrSy/NFgsvAnGMLsxFaD4fifdUFn5nF/EI0zrhuevZzE1ew0Tz+X/z2ADa8QgMPPnUFBCw/k8k7vWRTzoOO8y9o5M6n6vs2T+hECdpcaUSWUXWLy9FWwJmVG7sEv3e2L7KS6DnINV7AVDKhL32dOFQKBgBGsKG+W6Qs90qFZ92oF138csK1ewop4Fl8JiORQAVicEp1F7rd9HDZ+yn6VqkYI3eJ7W77IQPRAb39Nm36ebY/iiZ1q1b3VMzNPWk0JtLSUTR8asihn6UB/3phQE+dtmVgqqwTuV+J8l7Tor4JPACWTo2A72qbkNV923Y/Jzb0W"
	publicKey  = "MIIBCgKCAQEA4YYRanaqAryAxemV+A9UfbKglFapYPsH9MSR83hwrE1we4hG47sN2f+agT6DF1vnb7MocChQzs0puJzJLHY53FK00fOgSmQ7IoE2cB/ywabNm4ynVMuJD0QCumb9m6wd9vT47R6eKL5ZQ5lP0pzPjw632rzQfOwTGAQeq6Sg9R73NQbL3r6Kk0KorTgF6F+in0PwAiaSoKSKMPcF2sNdZONtPcx2XAEt/5DHz4XkgCEVtxZCw1/lJegjlP8j/8Uy4BjOSxAWMrIu3C9kJxP2Dftc8bBFs8YVEA019Q2IP22Ws35QgnQ9+YyesPLLnJgjLkifsNgSj457+LlWR/rtOQIDAQAB"
	keyx       = "941862746dc51adf517be9f820b4e558"
	baseURL    = "https://market.api.86698.cn"
	signType   = core.SignRSA
)

func newCore() *key.Key {
	c, _ := core.NewCore(&core.Config{
		AppID:      appId,
		PrivateKey: privateKey,
		PublicKey:  publicKey,
		Key:        keyx,
		SignType:   signType,
		BaseURL:    baseURL,
	})
	k := key.Key{c}

	return &k
}

func TestOrderx(t *testing.T) {
	bc := initialize.LoadConfig()
	ip := rpcimpl.NewYmtRpcImpl(bc, nil)
	fmt.Println(ip.GetKey(&key.OrderRequest{
		OutBizNo:   "**********",
		ActivityNo: "0627002",
		Number:     1,
		Account:    "***********",
		Extra:      "",
	}, "test01"))
	fmt.Println(ip.GetKey(&key.OrderRequest{
		OutBizNo:   "**********",
		ActivityNo: "0627002",
		Number:     1,
		Account:    "***********",
		Extra:      "",
	}, "jxjf"))
	fmt.Println(ip.GetKey(&key.OrderRequest{
		OutBizNo:   "**********",
		ActivityNo: "0627002",
		Number:     1,
		Account:    "***********",
		Extra:      "",
	}, "jxjf"))
	fmt.Println(ip.GetKey(&key.OrderRequest{
		OutBizNo:   "**********",
		ActivityNo: "0627002",
		Number:     1,
		Account:    "***********",
		Extra:      "",
	}, "jxjf"))
	fmt.Println(ip.GetKey(&key.OrderRequest{
		OutBizNo:   "**********",
		ActivityNo: "0627002",
		Number:     1,
		Account:    "***********",
		Extra:      "",
	}, "jxjf"))
	fmt.Println(ip.GetKey(&key.OrderRequest{
		OutBizNo:   "**********",
		ActivityNo: "0627002",
		Number:     1,
		Account:    "***********",
		Extra:      "",
	}, "test01"))
	fmt.Println(ip.GetKey(&key.OrderRequest{
		OutBizNo:   "**********",
		ActivityNo: "0627002",
		Number:     1,
		Account:    "***********",
		Extra:      "",
	}, "test01"))
	fmt.Println(ip.GetKey(&key.OrderRequest{
		OutBizNo:   "**********",
		ActivityNo: "0627002",
		Number:     1,
		Account:    "***********",
		Extra:      "",
	}, "test01"))
	fmt.Println(ip.GetKey(&key.OrderRequest{
		OutBizNo:   "**********",
		ActivityNo: "0627002",
		Number:     1,
		Account:    "***********",
		Extra:      "",
	}, "jxjf"))
	fmt.Println(ip.GetKey(&key.OrderRequest{
		OutBizNo:   "**********",
		ActivityNo: "0627002",
		Number:     1,
		Account:    "***********",
		Extra:      "",
	}, "test01"))
}

func TestOrder(t *testing.T) {
	c := newCore()

	_, r, err := c.Order(context.Background(), &key.OrderRequest{
		OutBizNo:   "**********",
		ActivityNo: "0627002",
		Number:     1,
		NotifyUrl:  "",
		Account:    "***********",
		Extra:      "",
	})
	if err != nil {
		t.Error(err)
		return
	}

	if !r.IsSuccess() {
		t.Errorf("获取key失败:%s", r.Message)
		return
	}

	t.Logf("data=%s", string(r.Data))
	t.Logf("headers=%+v", c.Headers)
}

func TestDES(t *testing.T) {
	// RSA公钥
	/*	pubKey := `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDUS/Ukcdn6hq5QAea+Ja1nMvDSB9Vy/DhzQKYs
		ZaYdtSZljTBZ6U3GKpr23zLoJq/X6lJMVVJW16dX9jfwhc/zSo1o+buUJlCYCX308F+ol/zPSzxo
		k1J1ruwqqnkDlHMyX251uDjDCpqvJCL8eqxrwKE2YdVkgLo5iJoQVFMDpQIDAQAB`*/

	// DES公钥
	tpDesKey := "Ty/0Pnbltc0OZ7rEFp1PV08v9D525bXN"

	// 收到的加密参数
	encryptData := "r+t2SrWa8MTZUykRCK2BPJUgSg1Ke16CIhDpqYGAT5LBBZ8uOQmCjXJlaQzQxkOXAQY0tie5Gj4nbFdyLwyFtdARGU0b4pGgcduuhGh6d/YUbzuVDhj4QonsBwiB6kjwBhQSz0LW3DF/s70VpNMUs1xOKK2e0UpRtKuFuQ2vqTwmoyL7dgXd760ypkm9WfveTMdR3YurwGjKAV6HrWrsQIMl453DQ4VRhYbMfr+ES/59A1XfUziXkp/ovjSmMbOAH3bYCRx0f6qems71zEjKzGwfL8xwKYP1oT7pshpR8onNrgpE9Dt7Wz3W2sykyiiu5+CZZViDoiM0eGGwJaXCX9UrnVcXpFG+D4tOZGACtGhxdhzco+9alA=="

	// Base64解码
	decodeBase64, err := base64.StdEncoding.DecodeString(encryptData)
	if err != nil {
		log.Fatalf("Base64 decode error: %v", err)
	}

	// 使用DES解密
	desKey, err := base64.StdEncoding.DecodeString(tpDesKey)
	if err != nil {
		log.Fatalf("DES key decode error: %v", err)
	}

	block, err := des.NewTripleDESCipher(desKey)
	if err != nil {
		log.Fatalf("DES cipher error: %v", err)
	}

	if len(decodeBase64)%block.BlockSize() != 0 {
		log.Fatalf("Cipher text length not aligned with block size")
	}

	cipherText := make([]byte, len(decodeBase64))
	block.Decrypt(cipherText, decodeBase64)

	// 输出解密后的内容
	jsonString := string(cipherText)
	fmt.Println("encryptData解密后:", jsonString)

}
